## LinkedIn: https://www.linkedin.com/in/saivigneshgolla/
## Copyright (C) 2024 Sai <PERSON><PERSON><PERSON>
## License: GNU Affero General Public License
## https://www.gnu.org/licenses/agpl-3.0.en.html
## GitHub: https://github.com/GodsScion/Auto_job_applier_linkedIn
## Check if Python3 is installed (macOS)
if ! command -v python3 &> /dev/null; then
    echo "Python3 is not installed or not accessible!"
    echo "Lütfen Python3'ü kurun ve PATH'e ekleyin."
    echo "https://www.python.org/downloads/"
    exit 1
else
    echo "Python3 is already installed."
fi
# Create and activate virtual environment for Python packages
if [ ! -d "venv" ]; then
    python3 -m venv venv
fi
source venv/bin/activate
python3 -m pip install --upgrade pip
python3 -m pip install selenium undetected-chromedriver
# Check if Google Chrome is installed (macOS)
if ! command -v /Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome &> /dev/null; then
    echo "Google Chrome is not installed!"
    echo "Lütfen Google Chrome'u kurun: https://www.google.com/chrome/"
    exit 1
else
    echo "Google Chrome is already installed."
fi
# Download latest ChromeDriver for macOS (auto detect platform)
arch_name=$(uname -m)
if [ "$arch_name" = "arm64" ]; then
    platform="mac-arm64"
else
    platform="mac-x64"
fi
latest_versions_info=$(curl -sS "https://googlechromelabs.github.io/chrome-for-testing/last-known-good-versions-with-downloads.json")
download_url=$(echo "$latest_versions_info" | grep -A 10 "\"platform\": \"$platform\"" | grep 'url":' | head -1 | awk -F'"' '{print $4}')
if [ -z "$download_url" ]; then
    echo "ChromeDriver indirme linki bulunamadı! Lütfen manuel kurulum yapın."
    exit 1
fi
echo "Download URL: $download_url"
curl -o chromedriver-mac.zip "$download_url"
chrome_install_dir="$HOME/chromedriver"
mkdir -p "$chrome_install_dir"
unzip -q chromedriver-mac.zip -d "$chrome_install_dir"
rm chromedriver-mac.zip
# Add ChromeDriver to PATH (zsh veya bash)
if [ "$SHELL" = "/bin/zsh" ]; then
    echo "export PATH=\"\$PATH:$chrome_install_dir\"" >> ~/.zshrc
    echo "PATH eklendi ~/.zshrc'a. Yeni terminal açınca aktif olur."
elif [ "$SHELL" = "/bin/bash" ]; then
    echo "export PATH=\"\$PATH:$chrome_install_dir\"" >> ~/.bash_profile
    echo "PATH eklendi ~/.bash_profile'a. Yeni terminal açınca aktif olur."
else
    echo "PATH değişkenini elle ekleyin: export PATH=\"\$PATH:$chrome_install_dir\""
fi
echo "Kurulum tamamlandı. Artık web scraping aracı kullanılabilir."
read -rsn1 -p "Devam etmek için bir tuşa basın..."
# # Get the latest ChromeDriver version
# LATEST_VERSION=$(curl -sS https://chromedriver.storage.googleapis.com/LATEST_RELEASE)
# # Download ChromeDriver
# echo "Installing latest version of Google Chrome Driver (${LATEST_VERSION})"
# CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/${LATEST_VERSION}/chromedriver_win32.zip"
# CHROMEDRIVER_FILE="chromedriver.zip"
# CHROMEDRIVER_DIR="chromedriver"
# # Download ChromeDriver using certutil
# certutil -urlcache -split -f $CHROMEDRIVER_URL $CHROMEDRIVER_FILE
# # Extract ChromeDriver zip
# unzip $CHROMEDRIVER_FILE -d $CHROMEDRIVER_DIR
# rm $CHROMEDRIVER_FILE
# # Get the absolute path to the current directory
# CURRENT_DIR=$(pwd)
# # Set up environment variables
# echo "setx CHROME_DRIVER_PATH \"${CURRENT_DIR}\\${CHROMEDRIVER_DIR}\\chromedriver.exe\"" >> setup_env.bat
# echo "setx PATH \"%PATH%;${CURRENT_DIR}\\${CHROMEDRIVER_DIR}\"" >> setup_env.bat
# # Run the environment setup script
# cmd.exe /c setup_env.bat
# echo "Setup complete. You can now use the web scraping tool."
# # Remove the environment setup script
# rm setup_env.bat
# read -p "Press any key to continue!"# Get the latest ChromeDriver version
# LATEST_VERSION=$(curl -sS https://chromedriver.storage.googleapis.com/LATEST_RELEASE)
# # Set the destination directory
# CHROME_INSTALL_DIR="C:\\Program Files\\Google\\Chrome"
# # Download ChromeDriver
# echo "Installing latest version of Google Chrome Driver (${LATEST_VERSION})"
# CHROMEDRIVER_URL="https://chromedriver.storage.googleapis.com/${LATEST_VERSION}/chromedriver_win32.zip"
# CHROMEDRIVER_FILE="chromedriver.zip"
# CHROMEDRIVER_DIR="$CHROME_INSTALL_DIR"
# # Create the destination directory if it doesn't exist
# mkdir -p "$CHROME_INSTALL_DIR"
# # Download ChromeDriver using certutil
# certutil -urlcache -split -f $CHROMEDRIVER_URL $CHROMEDRIVER_FILE
# # Extract ChromeDriver zip to the installation directory
# unzip $CHROMEDRIVER_FILE -d $CHROME_INSTALL_DIR
# rm $CHROMEDRIVER_FILE
# echo "Setup complete. You can now use the web scraping tool."
